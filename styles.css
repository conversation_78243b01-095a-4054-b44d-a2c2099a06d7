/* Mom's Handmade Greetings - Main Stylesheet */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Georgia', 'Times New Roman', serif;
    line-height: 1.6;
    color: #2C2C2C;
    background-color: #F5F1EB;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Arial', sans-serif;
    margin-bottom: 1rem;
    color: #8B4B6B;
}

h1 {
    font-size: 2.5rem;
    font-weight: 300;
}

h2 {
    font-size: 2rem;
    font-weight: 400;
}

h3 {
    font-size: 1.5rem;
    font-weight: 500;
}

p {
    margin-bottom: 1rem;
    max-width: 70ch;
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Header */
header {
    background-color: #D4A574;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #8B4B6B;
    text-decoration: none;
}

.contact-info {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: #2C2C2C;
}

.contact-info a {
    color: #2C2C2C;
    text-decoration: none;
}

.contact-info a:hover {
    color: #8B4B6B;
}

/* Navigation */
nav {
    background-color: #E8C5A0;
    padding: 1rem 0;
}

.nav-list {
    display: flex;
    justify-content: center;
    list-style: none;
    gap: 3rem;
    flex-wrap: wrap;
}

.nav-list a {
    color: #2C2C2C;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.nav-list a:hover,
.nav-list a.active {
    background-color: #D4A574;
    color: #8B4B6B;
}

/* Main Content */
main {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, #E8C5A0, #F5F1EB);
    margin-bottom: 3rem;
    border-radius: 10px;
}

.hero h1 {
    margin-bottom: 1rem;
    color: #8B4B6B;
}

.hero p {
    font-size: 1.2rem;
    margin: 0 auto 2rem;
    max-width: 60ch;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    background-color: #8B4B6B;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn:hover {
    background-color: #6B3A52;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #D4A574;
    color: #2C2C2C;
}

.btn-secondary:hover {
    background-color: #B8935F;
}

/* Card Grid */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-image {
    width: 100%;
    height: 200px;
    background-color: #E8C5A0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8B4B6B;
    font-size: 1.2rem;
}

.card-content {
    padding: 1.5rem;
}

.card h3 {
    margin-bottom: 0.5rem;
}

.card p {
    color: #666;
    font-size: 0.9rem;
}

.price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #8B4B6B;
    margin-top: 1rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #8B4B6B;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #E8C5A0;
    border-radius: 5px;
    font-size: 1rem;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #8B4B6B;
}

textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
footer {
    background-color: #8B4B6B;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: white;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #E8C5A0;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-2 {
    margin-bottom: 2rem;
}

.mt-2 {
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-list {
        gap: 1rem;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .card-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
    }
}
