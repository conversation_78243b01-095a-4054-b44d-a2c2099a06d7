<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery - Mom's Handmade Greetings</title>
    <meta name="description" content="Browse our collection of handmade greeting cards. Find the perfect card for birthdays, weddings, holidays, and special occasions.">
    <link rel="stylesheet" href="styles.css">
    <script src="https://kit.fontawesome.com/513f5bcc78.js" crossorigin="anonymous"></script>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    <i class="fas fa-heart"></i> Mom's Handmade Greetings
                </a>
                <div class="contact-info">
                    <div>
                        <i class="fas fa-phone"></i>
                        <a href="tel:+1234567890">(*************</a>
                    </div>
                    <div>
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul class="nav-list">
                <li><a href="index.html">Home</a></li>
                <li><a href="gallery.html" class="active">Gallery</a></li>
                <li><a href="product-details.html">Featured Card</a></li>
                <li><a href="custom-order.html">Custom Orders</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <div class="container">
            <!-- Page Header -->
            <section class="text-center mb-2">
                <h1>Our Card Gallery</h1>
                <p>Explore our beautiful collection of handmade greeting cards. Each card is carefully crafted with love and attention to detail, perfect for expressing your heartfelt sentiments.</p>
            </section>

            <!-- Filter Section -->
            <section class="mb-2">
                <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap; margin-bottom: 2rem;">
                    <button class="btn btn-secondary" onclick="filterCards('all')">All Cards</button>
                    <button class="btn btn-secondary" onclick="filterCards('birthday')">Birthday</button>
                    <button class="btn btn-secondary" onclick="filterCards('wedding')">Wedding</button>
                    <button class="btn btn-secondary" onclick="filterCards('holiday')">Holiday</button>
                    <button class="btn btn-secondary" onclick="filterCards('thank-you')">Thank You</button>
                    <button class="btn btn-secondary" onclick="filterCards('baby')">New Baby</button>
                </div>
            </section>

            <!-- Cards Gallery -->
            <section>
                <div class="card-grid">
                    <!-- Birthday Cards -->
                    <div class="card" data-category="birthday">
                        <div class="card-image">
                            <i class="fas fa-birthday-cake" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Birthday Celebration</h3>
                            <p>Vibrant watercolor flowers with elegant calligraphy</p>
                            <div class="price">$8.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <div class="card" data-category="birthday">
                        <div class="card-image">
                            <i class="fas fa-gift" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Birthday Surprise</h3>
                            <p>Colorful balloons and confetti design with gold accents</p>
                            <div class="price">$9.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <!-- Wedding Cards -->
                    <div class="card" data-category="wedding">
                        <div class="card-image">
                            <i class="fas fa-heart" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Wedding Wishes</h3>
                            <p>Elegant lace details with pearl accents</p>
                            <div class="price">$12.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <div class="card" data-category="wedding">
                        <div class="card-image">
                            <i class="fas fa-rings-wedding" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Forever Love</h3>
                            <p>Classic white and gold with embossed hearts</p>
                            <div class="price">$14.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <!-- Holiday Cards -->
                    <div class="card" data-category="holiday">
                        <div class="card-image">
                            <i class="fas fa-snowflake" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Holiday Magic</h3>
                            <p>Glittering snowflakes with warm holiday colors</p>
                            <div class="price">$9.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <div class="card" data-category="holiday">
                        <div class="card-image">
                            <i class="fas fa-tree" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Christmas Joy</h3>
                            <p>Hand-painted Christmas tree with tiny ornaments</p>
                            <div class="price">$10.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <!-- Thank You Cards -->
                    <div class="card" data-category="thank-you">
                        <div class="card-image">
                            <i class="fas fa-leaf" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Thank You Garden</h3>
                            <p>Pressed flowers with handwritten typography</p>
                            <div class="price">$7.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <div class="card" data-category="thank-you">
                        <div class="card-image">
                            <i class="fas fa-hands-helping" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Grateful Hearts</h3>
                            <p>Warm earth tones with heartfelt message</p>
                            <div class="price">$8.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <!-- Baby Cards -->
                    <div class="card" data-category="baby">
                        <div class="card-image">
                            <i class="fas fa-baby" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>New Baby Joy</h3>
                            <p>Soft pastels with tiny footprints and ribbon</p>
                            <div class="price">$10.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <div class="card" data-category="baby">
                        <div class="card-image">
                            <i class="fas fa-teddy-bear" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Sweet Dreams</h3>
                            <p>Adorable teddy bear design with lullaby theme</p>
                            <div class="price">$11.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <!-- Additional Cards -->
                    <div class="card" data-category="birthday">
                        <div class="card-image">
                            <i class="fas fa-graduation-cap" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Graduation Pride</h3>
                            <p>Gold foil accents with motivational quotes</p>
                            <div class="price">$11.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>

                    <div class="card" data-category="thank-you">
                        <div class="card-image">
                            <i class="fas fa-sun" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Sunshine Thanks</h3>
                            <p>Bright and cheerful with sunflower motifs</p>
                            <div class="price">$8.99</div>
                            <a href="product-details.html" class="btn" style="margin-top: 1rem; width: 100%; text-align: center;">View Details</a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Call to Action -->
            <section class="text-center mt-2">
                <h2>Don't See What You're Looking For?</h2>
                <p>We'd love to create a custom card just for you! Contact us to discuss your unique vision.</p>
                <a href="custom-order.html" class="btn">Order Custom Card</a>
            </section>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div>
                    <p>&copy; 2024 Mom's Handmade Greetings. All rights reserved.</p>
                </div>
                <div class="social-links">
                    <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                    <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="#" aria-label="Pinterest"><i class="fab fa-pinterest"></i></a>
                    <a href="#" aria-label="Etsy"><i class="fab fa-etsy"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function filterCards(category) {
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                if (category === 'all' || card.dataset.category === category) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
