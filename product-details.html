<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday Celebration Card - Mom's Handmade Greetings</title>
    <meta name="description" content="Beautiful handmade birthday card with vibrant watercolor flowers and elegant calligraphy. Customize your message for the perfect personal touch.">
    <link rel="stylesheet" href="styles.css">
    <script src="https://kit.fontawesome.com/513f5bcc78.js" crossorigin="anonymous"></script>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    <i class="fas fa-heart"></i> Mom's Handmade Greetings
                </a>
                <div class="contact-info">
                    <div>
                        <i class="fas fa-phone"></i>
                        <a href="tel:+1234567890">(*************</a>
                    </div>
                    <div>
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul class="nav-list">
                <li><a href="index.html">Home</a></li>
                <li><a href="gallery.html">Gallery</a></li>
                <li><a href="product-details.html" class="active">Featured Card</a></li>
                <li><a href="custom-order.html">Custom Orders</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <div class="container">
            <!-- Breadcrumb -->
            <nav style="margin-bottom: 2rem; font-size: 0.9rem;">
                <a href="index.html" style="color: #8B4B6B; text-decoration: none;">Home</a> 
                <span style="margin: 0 0.5rem; color: #666;">/</span>
                <a href="gallery.html" style="color: #8B4B6B; text-decoration: none;">Gallery</a>
                <span style="margin: 0 0.5rem; color: #666;">/</span>
                <span style="color: #666;">Birthday Celebration</span>
            </nav>

            <!-- Product Details Section -->
            <section style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; margin-bottom: 3rem;">
                <!-- Product Image -->
                <div>
                    <div class="card-image" style="height: 400px; border-radius: 10px; margin-bottom: 1rem;">
                        <i class="fas fa-birthday-cake" style="font-size: 5rem;"></i>
                    </div>
                    <p style="text-align: center; color: #666; font-size: 0.9rem;">
                        <i class="fas fa-info-circle"></i> 
                        Actual card may vary slightly from image shown
                    </p>
                </div>

                <!-- Product Information -->
                <div>
                    <h1>Birthday Celebration Card</h1>
                    <div class="price" style="font-size: 2rem; margin: 1rem 0;">$8.99</div>
                    
                    <div style="margin-bottom: 2rem;">
                        <h3>Description</h3>
                        <p>This beautiful birthday card features vibrant watercolor flowers painted by hand, complemented by elegant calligraphy that adds a personal touch to your special message. Each card is unique, with slight variations that make it truly one-of-a-kind.</p>
                        
                        <p>Perfect for celebrating birthdays of all ages, this card combines artistic beauty with heartfelt sentiment. The high-quality cardstock ensures durability while maintaining an elegant feel.</p>
                    </div>

                    <div style="margin-bottom: 2rem;">
                        <h3>Product Details</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin-bottom: 0.5rem;"><strong>Size:</strong> 5" x 7" (A7)</li>
                            <li style="margin-bottom: 0.5rem;"><strong>Material:</strong> Premium cardstock (300gsm)</li>
                            <li style="margin-bottom: 0.5rem;"><strong>Finish:</strong> Matte with watercolor texture</li>
                            <li style="margin-bottom: 0.5rem;"><strong>Envelope:</strong> Matching colored envelope included</li>
                            <li style="margin-bottom: 0.5rem;"><strong>Interior:</strong> Blank for your personal message</li>
                            <li style="margin-bottom: 0.5rem;"><strong>Processing Time:</strong> 2-3 business days</li>
                        </ul>
                    </div>

                    <!-- Customization Options -->
                    <div style="margin-bottom: 2rem;">
                        <h3>Customize Your Card</h3>
                        
                        <div class="form-group">
                            <label for="message-type">Message Type:</label>
                            <select id="message-type" name="message-type">
                                <option value="blank">Blank (I'll write my own)</option>
                                <option value="happy-birthday">Happy Birthday</option>
                                <option value="custom">Custom Message (+$2.00)</option>
                            </select>
                        </div>

                        <div class="form-group" id="custom-message-group" style="display: none;">
                            <label for="custom-message">Your Custom Message:</label>
                            <textarea id="custom-message" name="custom-message" placeholder="Enter your personalized message here..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="envelope-color">Envelope Color:</label>
                            <select id="envelope-color" name="envelope-color">
                                <option value="cream">Cream (Default)</option>
                                <option value="white">White</option>
                                <option value="pink">Soft Pink</option>
                                <option value="lavender">Lavender</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="quantity">Quantity:</label>
                            <select id="quantity" name="quantity">
                                <option value="1">1 Card</option>
                                <option value="2">2 Cards</option>
                                <option value="3">3 Cards</option>
                                <option value="5">5 Cards (10% discount)</option>
                                <option value="10">10 Cards (15% discount)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Add to Cart Button -->
                    <button class="btn" style="width: 100%; font-size: 1.2rem; padding: 1.2rem;" onclick="addToCart()">
                        <i class="fas fa-shopping-cart"></i> Add to Cart
                    </button>

                    <div style="margin-top: 1rem; text-align: center;">
                        <p style="font-size: 0.9rem; color: #666;">
                            <i class="fas fa-truck"></i> Free shipping on orders over $25
                        </p>
                    </div>
                </div>
            </section>

            <!-- Additional Information -->
            <section style="margin-bottom: 3rem;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                    <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                        <h3><i class="fas fa-palette"></i> Handcrafted Quality</h3>
                        <p>Each card is individually hand-painted using high-quality watercolors and finished with care. No two cards are exactly alike, making each one special and unique.</p>
                    </div>
                    
                    <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                        <h3><i class="fas fa-leaf"></i> Eco-Friendly Materials</h3>
                        <p>We use sustainably sourced paper and eco-friendly inks. Our packaging is minimal and recyclable, reflecting our commitment to environmental responsibility.</p>
                    </div>
                    
                    <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                        <h3><i class="fas fa-heart"></i> Made with Love</h3>
                        <p>Every card is created with attention to detail and genuine care. We believe that handmade items carry a special energy that mass-produced cards simply cannot match.</p>
                    </div>
                </div>
            </section>

            <!-- Related Products -->
            <section>
                <h2 class="text-center mb-2">You Might Also Like</h2>
                <div class="card-grid">
                    <div class="card">
                        <div class="card-image">
                            <i class="fas fa-gift" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Birthday Surprise</h3>
                            <p>Colorful balloons and confetti design</p>
                            <div class="price">$9.99</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-image">
                            <i class="fas fa-star" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Special Day</h3>
                            <p>Golden stars with elegant script</p>
                            <div class="price">$10.99</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-image">
                            <i class="fas fa-flower" style="font-size: 3rem;"></i>
                        </div>
                        <div class="card-content">
                            <h3>Floral Wishes</h3>
                            <p>Delicate pressed flower arrangement</p>
                            <div class="price">$8.99</div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div>
                    <p>&copy; 2024 Mom's Handmade Greetings. All rights reserved.</p>
                </div>
                <div class="social-links">
                    <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                    <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="#" aria-label="Pinterest"><i class="fab fa-pinterest"></i></a>
                    <a href="#" aria-label="Etsy"><i class="fab fa-etsy"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Show/hide custom message field
        document.getElementById('message-type').addEventListener('change', function() {
            const customGroup = document.getElementById('custom-message-group');
            if (this.value === 'custom') {
                customGroup.style.display = 'block';
            } else {
                customGroup.style.display = 'none';
            }
        });

        function addToCart() {
            alert('Card added to cart! (This is a demo - no actual cart functionality)');
        }

        // Responsive design for product layout
        if (window.innerWidth <= 768) {
            const productSection = document.querySelector('section[style*="grid-template-columns: 1fr 1fr"]');
            if (productSection) {
                productSection.style.gridTemplateColumns = '1fr';
                productSection.style.gap = '2rem';
            }
        }
    </script>
</body>
</html>
