<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Order Form - Mom's Handmade Greetings</title>
    <meta name="description" content="Order a custom handmade greeting card designed specifically for your special occasion. Personalized designs created with love and attention to detail.">
    <link rel="stylesheet" href="styles.css">
    <script src="https://kit.fontawesome.com/513f5bcc78.js" crossorigin="anonymous"></script>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">
                    <i class="fas fa-heart"></i> Mom's Handmade Greetings
                </a>
                <div class="contact-info">
                    <div>
                        <i class="fas fa-phone"></i>
                        <a href="tel:+1234567890">(*************</a>
                    </div>
                    <div>
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul class="nav-list">
                <li><a href="index.html">Home</a></li>
                <li><a href="gallery.html">Gallery</a></li>
                <li><a href="product-details.html">Featured Card</a></li>
                <li><a href="custom-order.html" class="active">Custom Orders</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <div class="container">
            <!-- Page Header -->
            <section class="text-center mb-2">
                <h1>Create Your Custom Card</h1>
                <p>Let us design a unique, handmade greeting card just for you! Fill out the form below with your vision, and we'll bring it to life with our artistic touch and attention to detail.</p>
            </section>

            <!-- Custom Order Process -->
            <section class="mb-2">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 3rem;">
                    <div style="text-align: center; padding: 1.5rem; background: white; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                        <div style="font-size: 3rem; color: #8B4B6B; margin-bottom: 1rem;">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h3>1. Share Your Vision</h3>
                        <p>Tell us about your occasion, preferred colors, style, and any special elements you'd like included.</p>
                    </div>
                    
                    <div style="text-align: center; padding: 1.5rem; background: white; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                        <div style="font-size: 3rem; color: #8B4B6B; margin-bottom: 1rem;">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h3>2. We Create</h3>
                        <p>Our artist will design and handcraft your unique card, incorporating your ideas with our artistic expertise.</p>
                    </div>
                    
                    <div style="text-align: center; padding: 1.5rem; background: white; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                        <div style="font-size: 3rem; color: #8B4B6B; margin-bottom: 1rem;">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <h3>3. Delivered to You</h3>
                        <p>Your custom card will be carefully packaged and shipped to you within 5-7 business days.</p>
                    </div>
                </div>
            </section>

            <!-- Custom Order Form -->
            <section>
                <div style="max-width: 800px; margin: 0 auto; background: white; padding: 3rem; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <h2 style="text-align: center; margin-bottom: 2rem;">Custom Order Form</h2>
                    
                    <form id="custom-order-form" onsubmit="submitOrder(event)">
                        <!-- Personal Information -->
                        <fieldset style="border: 2px solid #E8C5A0; border-radius: 10px; padding: 2rem; margin-bottom: 2rem;">
                            <legend style="padding: 0 1rem; font-weight: bold; color: #8B4B6B;">Personal Information</legend>
                            
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div class="form-group">
                                    <label for="first-name">First Name *</label>
                                    <input type="text" id="first-name" name="first-name" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="last-name">Last Name *</label>
                                    <input type="text" id="last-name" name="last-name" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone">
                            </div>
                        </fieldset>

                        <!-- Card Details -->
                        <fieldset style="border: 2px solid #E8C5A0; border-radius: 10px; padding: 2rem; margin-bottom: 2rem;">
                            <legend style="padding: 0 1rem; font-weight: bold; color: #8B4B6B;">Card Details</legend>
                            
                            <div class="form-group">
                                <label for="occasion">Occasion/Event Type *</label>
                                <select id="occasion" name="occasion" required>
                                    <option value="">Select an occasion</option>
                                    <option value="birthday">Birthday</option>
                                    <option value="wedding">Wedding</option>
                                    <option value="anniversary">Anniversary</option>
                                    <option value="baby-shower">Baby Shower</option>
                                    <option value="graduation">Graduation</option>
                                    <option value="thank-you">Thank You</option>
                                    <option value="sympathy">Sympathy</option>
                                    <option value="holiday">Holiday</option>
                                    <option value="other">Other (please specify)</option>
                                </select>
                            </div>
                            
                            <div class="form-group" id="other-occasion-group" style="display: none;">
                                <label for="other-occasion">Please specify the occasion:</label>
                                <input type="text" id="other-occasion" name="other-occasion">
                            </div>
                            
                            <div class="form-group">
                                <label for="card-size">Preferred Card Size</label>
                                <select id="card-size" name="card-size">
                                    <option value="standard">Standard (5" x 7") - $15</option>
                                    <option value="large">Large (6" x 8") - $20</option>
                                    <option value="square">Square (6" x 6") - $18</option>
                                    <option value="mini">Mini (4" x 5") - $12</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="color-scheme">Preferred Color Scheme</label>
                                <input type="text" id="color-scheme" name="color-scheme" placeholder="e.g., soft pastels, vibrant blues and greens, elegant gold and cream">
                            </div>
                            
                            <div class="form-group">
                                <label for="style-preference">Style Preference</label>
                                <select id="style-preference" name="style-preference">
                                    <option value="">No preference</option>
                                    <option value="watercolor">Watercolor painting</option>
                                    <option value="pressed-flowers">Pressed flowers</option>
                                    <option value="calligraphy">Hand lettering/calligraphy</option>
                                    <option value="collage">Paper collage</option>
                                    <option value="minimalist">Minimalist design</option>
                                    <option value="vintage">Vintage style</option>
                                    <option value="modern">Modern/contemporary</option>
                                </select>
                            </div>
                        </fieldset>

                        <!-- Message and Special Requests -->
                        <fieldset style="border: 2px solid #E8C5A0; border-radius: 10px; padding: 2rem; margin-bottom: 2rem;">
                            <legend style="padding: 0 1rem; font-weight: bold; color: #8B4B6B;">Message & Special Requests</legend>
                            
                            <div class="form-group">
                                <label for="card-message">Message for Inside the Card</label>
                                <textarea id="card-message" name="card-message" placeholder="Enter the message you'd like written inside the card (optional - you can also leave it blank to write your own)"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="special-elements">Special Elements or Themes</label>
                                <textarea id="special-elements" name="special-elements" placeholder="Describe any specific elements you'd like included (e.g., specific flowers, symbols, quotes, names, dates, etc.)"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="inspiration">Additional Details or Inspiration</label>
                                <textarea id="inspiration" name="inspiration" placeholder="Share any additional thoughts, inspiration, or specific requests that will help us create the perfect card for you"></textarea>
                            </div>
                        </fieldset>

                        <!-- File Upload -->
                        <fieldset style="border: 2px solid #E8C5A0; border-radius: 10px; padding: 2rem; margin-bottom: 2rem;">
                            <legend style="padding: 0 1rem; font-weight: bold; color: #8B4B6B;">Reference Images (Optional)</legend>
                            
                            <div class="form-group">
                                <label for="reference-images">Upload Reference Images</label>
                                <input type="file" id="reference-images" name="reference-images" multiple accept="image/*">
                                <p style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;">
                                    You can upload images that inspire your vision or show examples of styles you like. Maximum 5 files, 5MB each.
                                </p>
                            </div>
                        </fieldset>

                        <!-- Quantity and Timeline -->
                        <fieldset style="border: 2px solid #E8C5A0; border-radius: 10px; padding: 2rem; margin-bottom: 2rem;">
                            <legend style="padding: 0 1rem; font-weight: bold; color: #8B4B6B;">Order Details</legend>
                            
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div class="form-group">
                                    <label for="quantity">Quantity Needed</label>
                                    <select id="quantity" name="quantity">
                                        <option value="1">1 card</option>
                                        <option value="2">2 cards</option>
                                        <option value="3">3 cards</option>
                                        <option value="5">5 cards</option>
                                        <option value="10">10 cards</option>
                                        <option value="other">Other (please specify)</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="needed-by">When do you need this?</label>
                                    <input type="date" id="needed-by" name="needed-by">
                                </div>
                            </div>
                            
                            <div class="form-group" id="other-quantity-group" style="display: none;">
                                <label for="other-quantity">Please specify quantity:</label>
                                <input type="number" id="other-quantity" name="other-quantity" min="1">
                            </div>
                        </fieldset>

                        <!-- Submit Button -->
                        <div style="text-align: center; margin-top: 2rem;">
                            <button type="submit" class="btn" style="font-size: 1.2rem; padding: 1.2rem 3rem;">
                                <i class="fas fa-paper-plane"></i> Submit Custom Order Request
                            </button>
                            <p style="font-size: 0.9rem; color: #666; margin-top: 1rem;">
                                We'll review your request and contact you within 24 hours with a quote and timeline.
                            </p>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Pricing Information -->
            <section class="mt-2">
                <div style="background: #E8C5A0; padding: 2rem; border-radius: 10px; text-align: center;">
                    <h3>Custom Card Pricing</h3>
                    <p>Custom cards start at $15 for standard size. Final pricing depends on complexity, size, and materials used. Rush orders (needed within 3 days) incur a 50% surcharge.</p>
                    <p><strong>Free shipping on all custom orders!</strong></p>
                </div>
            </section>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div>
                    <p>&copy; 2024 Mom's Handmade Greetings. All rights reserved.</p>
                </div>
                <div class="social-links">
                    <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                    <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="#" aria-label="Pinterest"><i class="fab fa-pinterest"></i></a>
                    <a href="#" aria-label="Etsy"><i class="fab fa-etsy"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Show/hide other occasion field
        document.getElementById('occasion').addEventListener('change', function() {
            const otherGroup = document.getElementById('other-occasion-group');
            if (this.value === 'other') {
                otherGroup.style.display = 'block';
            } else {
                otherGroup.style.display = 'none';
            }
        });

        // Show/hide other quantity field
        document.getElementById('quantity').addEventListener('change', function() {
            const otherGroup = document.getElementById('other-quantity-group');
            if (this.value === 'other') {
                otherGroup.style.display = 'block';
            } else {
                otherGroup.style.display = 'none';
            }
        });

        function submitOrder(event) {
            event.preventDefault();
            alert('Thank you for your custom order request! We will contact you within 24 hours with a quote and timeline. (This is a demo - no actual form submission)');
        }
    </script>
</body>
</html>
